#!/usr/bin/env python3
"""
测试 model 字段传递是否正确修复
"""
import asyncio
import json
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app.agents.translation.translation_agent import TranslationAgent
    from app.utils.logger import get_logger
    logger = get_logger(__name__)
    print("成功导入模块")
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

async def test_model_parameter():
    """测试 model 参数是否正确传递"""
    
    # 创建翻译 agent
    agent = TranslationAgent()
    
    # 测试数据
    question = [{"sourceText": "Hello, world!"}]
    translate_options = {
        "src_lang": "en",
        "tgt_lang": "zh",
        "provider": "llm_translate"
    }
    model = "ht::saas-deepseek-v3"  # 指定的模型
    
    print(f"=== 测试 model 参数传递 ===")
    print(f"指定的模型: {model}")
    print(f"翻译选项: {translate_options}")
    print(f"待翻译文本: {question}")
    
    try:
        # 调用翻译方法
        result = await agent.process_translation(
            question=question,
            stream=False,
            translate_options=translate_options,
            model=model
        )
        
        print(f"=== 翻译结果 ===")
        print(f"结果类型: {type(result)}")
        if isinstance(result, dict):
            print(f"结果代码: {result.get('code', 'unknown')}")
            print(f"结果消息: {result.get('message', 'unknown')}")
            if 'data' in result and result['data']:
                print(f"翻译结果: {result['data'][0].get('text', 'unknown')}")
        else:
            print(f"结果内容: {result}")
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        logger.error(f"测试异常: {str(e)}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(test_model_parameter())
